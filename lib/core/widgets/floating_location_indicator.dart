import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/generated/l10n.dart';

/// Enhanced floating location indicator that shows current city
class FloatingLocationIndicator extends StatefulWidget {
  final String? currentCity;
  final bool isLoading;
  final VoidCallback? onTap;
  final bool showPulse;

  const FloatingLocationIndicator({
    super.key,
    this.currentCity,
    this.isLoading = false,
    this.onTap,
    this.showPulse = true,
  });

  @override
  State<FloatingLocationIndicator> createState() => _FloatingLocationIndicatorState();
}

class _FloatingLocationIndicatorState extends State<FloatingLocationIndicator>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    // Pulse animation for location icon
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Slide animation for entrance
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    // Start animations
    _slideController.forward();
    if (widget.showPulse) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(FloatingLocationIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.showPulse && !oldWidget.showPulse) {
      _pulseController.repeat(reverse: true);
    } else if (!widget.showPulse && oldWidget.showPulse) {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final isRTL = context.read<LocaleCubit>().isArabic();
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        margin: EdgeInsets.only(
          left: isRTL ? 0 : 16,
          right: isRTL ? 16 : 0,
          bottom: 90, // Above bottom navigation
        ),
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(25),
          shadowColor: Colors.black.withValues(alpha: 0.3),
          child: InkWell(
            onTap: widget.onTap,
            borderRadius: BorderRadius.circular(25),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    isDark ? AppColors.darkGrey : AppColors.white,
                    isDark ? AppColors.darkGrey2 : AppColors.lightGrey,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: AppColors.yellow.withValues(alpha: 0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.yellow.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Animated location icon
                  if (widget.isLoading)
                    _buildLoadingIcon()
                  else
                    _buildLocationIcon(),
                  
                  const SizedBox(width: 8),
                  
                  // City text with animation
                  _buildCityText(s, isDark),
                  
                  // Arrow indicator
                  if (widget.onTap != null) ...[
                    const SizedBox(width: 8),
                    Icon(
                      isRTL ? Icons.arrow_back_ios : Icons.arrow_forward_ios,
                      size: 14,
                      color: AppColors.yellow,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingIcon() {
    return SizedBox(
      width: 20,
      height: 20,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.yellow),
      ),
    );
  }

  Widget _buildLocationIcon() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.showPulse ? _pulseAnimation.value : 1.0,
          child: Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.yellow.withValues(alpha: 0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.location_on_rounded,
              size: 14,
              color: AppColors.yellow,
            ),
          ),
        );
      },
    );
  }

  Widget _buildCityText(S s, bool isDark) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: Text(
        widget.currentCity ?? s.detectingLocation,
        key: ValueKey(widget.currentCity ?? 'loading'),
        style: AppTextStyles.font12SemiBold.copyWith(
          color: isDark ? Colors.white : AppColors.darkGrey,
        ),
      ),
    );
  }
}

/// Enhanced location status bar for top of screen
class LocationStatusBar extends StatelessWidget {
  final String? currentCity;
  final bool isLoading;
  final VoidCallback? onTap;

  const LocationStatusBar({
    super.key,
    this.currentCity,
    this.isLoading = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final isRTL = context.read<LocaleCubit>().isArabic();
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        elevation: 2,
        borderRadius: BorderRadius.circular(20),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isDark ? AppColors.darkGrey : AppColors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppColors.yellow.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Location icon
                TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 1500),
                  tween: Tween(begin: 0.0, end: 1.0),
                  builder: (context, value, child) {
                    return Transform.rotate(
                      angle: value * 2 * 3.14159,
                      child: Icon(
                        Icons.my_location_rounded,
                        size: 16,
                        color: AppColors.yellow,
                      ),
                    );
                  },
                ),
                
                const SizedBox(width: 8),
                
                // Status text
                if (isLoading)
                  Text(
                    s.detectingLocation,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: AppColors.yellow,
                    ),
                  )
                else
                  Text(
                    currentCity ?? s.selectCity,
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: isDark ? Colors.white : AppColors.darkGrey,
                    ),
                  ),
                
                if (onTap != null) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.keyboard_arrow_down_rounded,
                    size: 16,
                    color: AppColors.yellow,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
