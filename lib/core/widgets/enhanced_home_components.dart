import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/generated/l10n.dart';

/// Enhanced Header Component for Home Page
class EnhancedHomeHeader extends StatelessWidget {
  final String appName;
  final String? currentCity;
  final VoidCallback? onCityTap;
  final VoidCallback? onSettingsTap;
  final bool isLoadingLocation;

  const EnhancedHomeHeader({
    super.key,
    required this.appName,
    this.currentCity,
    this.onCityTap,
    this.onSettingsTap,
    this.isLoadingLocation = false,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return EnhancedCard(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Logo and App Info
          Expanded(
            child: Row(
              children: [
                // Enhanced Logo Container
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        context.accentColor.withValues(alpha: 0.1),
                        context.accentColor.withValues(alpha: 0.05),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: context.accentColor.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Image.asset(
                    AppAssets.imagesLogoCircle,
                    width: 44,
                    height: 44,
                  ),
                ),
                const SizedBox(width: 16),
                // App Name and Location
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        appName,
                        style: AppTextStyles.font20Bold.copyWith(
                          color: context.primaryTextColor,
                        ),
                        overflow: TextOverflow.ellipsis, // Prevent overflow
                      ),
                      const SizedBox(height: 6),
                      // Enhanced Location Selector
                      GestureDetector(
                        onTap: onCityTap,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 3,
                          ),
                          decoration: BoxDecoration(
                            color: context.accentColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: context.accentColor.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.location_on_rounded,
                                size: 16,
                                color: context.accentColor,
                              ),
                              const SizedBox(width: 6),
                              if (isLoadingLocation)
                                SizedBox(
                                  width: 12,
                                  height: 12,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      context.accentColor,
                                    ),
                                  ),
                                )
                              else
                                Flexible(
                                  child: Text(
                                    currentCity ?? s.detectingLocation,
                                    style: AppTextStyles.font12SemiBold.copyWith(
                                      color: context.accentColor,
                                    ),
                                    overflow: TextOverflow.ellipsis, // Prevent overflow
                                  ),
                                ),
                              const SizedBox(width: 4),
                              Icon(
                                Icons.keyboard_arrow_down_rounded,
                                size: 16,
                                color: context.accentColor,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Settings Button
          Container(
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(14),
              border: Border.all(
                color: context.accentColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: onSettingsTap,
              icon: Icon(
                Icons.settings_rounded,
                size: 22,
                color: context.accentColor,
              ),
              tooltip: s.settings,
            ),
          ),
        ],
      ),
    );
  }
}

/// Enhanced Search Bar Component
class EnhancedSearchBar extends StatelessWidget {
  final String? hintText;
  final TextEditingController? controller;
  final VoidCallback? onFilterTap;
  final VoidCallback? onSearchTap;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final bool showFilter;
  final bool isLoading;

  const EnhancedSearchBar({
    super.key,
    this.hintText,
    this.controller,
    this.onFilterTap,
    this.onSearchTap,
    this.onChanged,
    this.onSubmitted,
    this.showFilter = true,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: context.accentColor.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: TextField(
        controller: controller,
        onChanged: onChanged,
        onTap: onSearchTap,
        onSubmitted: onSubmitted,
        style: AppTextStyles.font16Regular.copyWith(
          color: context.primaryTextColor,
        ),
        decoration: InputDecoration(
          hintText: hintText ?? s.searchHint,
          hintStyle: AppTextStyles.font16Regular.copyWith(
            color: context.secondaryTextColor,
          ),
          prefixIcon: Container(
            padding: const EdgeInsets.all(14),
            child: Icon(
              Icons.search_rounded,
              color: context.accentColor,
              size: 24,
            ),
          ),
          suffixIcon: isLoading
              ? Container(
                  margin: const EdgeInsets.all(12),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        context.accentColor,
                      ),
                    ),
                  ),
                )
              : showFilter
                  ? Container(
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            context.accentColor,
                            context.accentColor.withValues(alpha: 0.8),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(14),
                        boxShadow: [
                          BoxShadow(
                            color: context.accentColor.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: IconButton(
                        onPressed: onFilterTap,
                        icon: const Icon(
                          Icons.tune_rounded,
                          color: Colors.white,
                          size: 20,
                        ),
                        tooltip: s.filter,
                      ),
                    )
                  : null,
          filled: true,
          fillColor: context.cardColor,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: context.secondaryTextColor.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(20),
            borderSide: BorderSide(
              color: context.accentColor,
              width: 2,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 18,
            horizontal: 20,
          ),
        ),
      ),
    );
  }
}

/// Enhanced Category Filter Button
class EnhancedCategoryFilter extends StatelessWidget {
  final String label;
  final String imageUrl;
  final bool isSelected;
  final VoidCallback onTap;

  const EnhancedCategoryFilter({
    super.key,
    required this.label,
    required this.imageUrl,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [
                    context.accentColor,
                    context.accentColor.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
          color: isSelected
              ? null
              : (context.isDarkMode ? Colors.grey[800] : Colors.grey[100]),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isSelected
                ? context.accentColor
                : context.secondaryTextColor.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: context.accentColor.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                    spreadRadius: 0,
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                imageUrl,
                width: 24,
                height: 24,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: context.secondaryTextColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.image_not_supported_rounded,
                    size: 16,
                    color: context.secondaryTextColor,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: AppTextStyles.font14SemiBold.copyWith(
                color: isSelected
                    ? Colors.white
                    : context.primaryTextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Enhanced Section Header with Animation
class EnhancedSectionHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final VoidCallback? onViewAll;
  final String? viewAllText;

  const EnhancedSectionHeader({
    super.key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.onViewAll,
    this.viewAllText,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Icon Container with Gradient
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  context.accentColor.withValues(alpha: 0.15),
                  context.accentColor.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(14),
              border: Border.all(
                color: context.accentColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: context.accentColor,
              size: 22,
            ),
          ),
          const SizedBox(width: 16),
          // Title and Subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font20Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                  overflow: TextOverflow.ellipsis, // Prevent overflow
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    subtitle!,
                    style: AppTextStyles.font12Regular.copyWith(
                      color: context.secondaryTextColor,
                    ),
                    overflow: TextOverflow.ellipsis, // Prevent overflow
                  ),
                ],
              ],
            ),
          ),
          // View All Button
          if (onViewAll != null)
            Flexible(
              child: TextButton.icon(
                onPressed: onViewAll,
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  textStyle: const TextStyle(inherit: true), // Ensure default is inherit: true
                ),
                icon: Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 14,
                  color: context.accentColor,
                ),
                label: Text(
                  viewAllText ?? s.viewAll,
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.accentColor,
                    inherit: true, // Redundant but explicit
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
