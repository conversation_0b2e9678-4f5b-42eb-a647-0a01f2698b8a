import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/utils/sound_manager.dart';
import 'package:gather_point/feature/auth/presentation/views/widgets/phone_login_bottom_sheet.dart';
import 'package:gather_point/generated/l10n.dart';

class LoginButtonSection extends StatelessWidget {
  const LoginButtonSection({super.key});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return Column(
      children: [
        // Phone Login Button - Enhanced Design
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.yellow.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Material(
            color: AppColors.black,
            borderRadius: BorderRadius.circular(12),
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: () {
                SoundManager.playClickSound();
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (context) => const PhoneLoginBottomSheet(),
                );
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.phone_android,
                      color: AppColors.yellow,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      s.proceedWithPhone,
                      style: const TextStyle(
                        color: AppColors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
