import 'package:flutter/material.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/feature/host/data/models/withdrawal_model.dart';
import 'package:gather_point/feature/host/data/services/withdrawal_api_service.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/services/service_locator.dart';

class WithdrawalManagementPage extends StatefulWidget {
  const WithdrawalManagementPage({super.key});

  @override
  State<WithdrawalManagementPage> createState() => _WithdrawalManagementPageState();
}

class _WithdrawalManagementPageState extends State<WithdrawalManagementPage> {
  final WithdrawalApiService _withdrawalService = getIt<WithdrawalApiService>();
  List<WithdrawalModel> _withdrawals = [];
  List<WithdrawalMethod> _methods = [];
  bool _isLoading = true;
  final double _walletBalance = 2450.75; // This should come from the dashboard state

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final withdrawals = await _withdrawalService.getWithdrawalHistory();
      final methods = await _withdrawalService.getWithdrawalMethods();
      
      setState(() {
        _withdrawals = withdrawals;
        _methods = methods;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: 'إدارة السحب',
      hasBottomNavigation: false,
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showWithdrawDialog(context),
        backgroundColor: AppColors.yellow,
        foregroundColor: AppColors.black,
        icon: const Icon(Icons.add_rounded),
        label: const Text('طلب سحب جديد'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Wallet Balance Card
                    _buildWalletBalanceCard(),
                    
                    const SizedBox(height: 24),
                    
                    // Withdrawal History
                    _buildWithdrawalHistory(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildWalletBalanceCard() {
    return EnhancedCard(
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppColors.yellow.withValues(alpha: 0.1),
              AppColors.yellow.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.yellow.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet_rounded,
                    color: AppColors.yellow,
                    size: 24,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.trending_up_rounded,
                        color: Colors.green,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'متاح للسحب',
                        style: AppTextStyles.font12SemiBold.copyWith(
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'الرصيد المتاح',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '\$${_walletBalance.toStringAsFixed(2)}',
              style: AppTextStyles.font30SemiBold.copyWith(
                color: AppColors.yellow,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWithdrawalHistory() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'سجل السحب',
          style: AppTextStyles.font20Bold,
        ),
        const SizedBox(height: 16),
        
        if (_withdrawals.isEmpty)
          _buildEmptyState()
        else
          ..._withdrawals.map((withdrawal) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildWithdrawalCard(withdrawal),
          )),
      ],
    );
  }

  Widget _buildEmptyState() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(40),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.receipt_long_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد عمليات سحب',
                style: AppTextStyles.font18Bold.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'ستظهر هنا جميع طلبات السحب الخاصة بك',
                style: AppTextStyles.font14Regular.copyWith(
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWithdrawalCard(WithdrawalModel withdrawal) {
    final statusColor = _getStatusColor(withdrawal.status);
    
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      withdrawal.reference,
                      style: AppTextStyles.font16Bold,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      withdrawal.methodText,
                      style: AppTextStyles.font14Regular.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    withdrawal.statusText,
                    style: AppTextStyles.font12SemiBold.copyWith(
                      color: statusColor,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Amount details
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  _buildAmountRow('المبلغ المطلوب:', withdrawal.formattedAmount),
                  if (withdrawal.fees > 0) ...[
                    const SizedBox(height: 8),
                    _buildAmountRow('الرسوم:', '-${withdrawal.formattedFees}'),
                  ],
                  const SizedBox(height: 8),
                  const Divider(),
                  const SizedBox(height: 8),
                  _buildAmountRow(
                    'المبلغ الصافي:',
                    withdrawal.formattedNetAmount,
                    isTotal: true,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Time and actions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  withdrawal.timeSinceRequestText,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
                if (withdrawal.canBeCancelled)
                  TextButton(
                    onPressed: () => _cancelWithdrawal(withdrawal),
                    child: Text(
                      'إلغاء',
                      style: AppTextStyles.font14SemiBold.copyWith(
                        color: Colors.red,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountRow(String label, String amount, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isTotal 
              ? AppTextStyles.font14SemiBold
              : AppTextStyles.font14Regular.copyWith(color: Colors.grey[600]),
        ),
        Text(
          amount,
          style: isTotal 
              ? AppTextStyles.font16Bold.copyWith(color: AppColors.yellow)
              : AppTextStyles.font14SemiBold,
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'processing':
        return Colors.blue;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.grey;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _showWithdrawDialog(BuildContext context) {
    // Implementation will be added in the next part
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طلب سحب جديد'),
        content: const Text('سيتم تطوير هذه الميزة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelWithdrawal(WithdrawalModel withdrawal) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء طلب السحب'),
        content: Text('هل أنت متأكد من إلغاء طلب السحب ${withdrawal.reference}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('لا'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('نعم'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _withdrawalService.cancelWithdrawal(withdrawal.id);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إلغاء طلب السحب بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
          _loadData();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إلغاء طلب السحب: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
