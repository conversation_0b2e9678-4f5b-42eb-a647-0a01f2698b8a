import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/services/location_service.dart';
import 'package:gather_point/core/widgets/enhanced_home_components.dart'
    as home_components;
import 'package:gather_point/core/widgets/shimmer_components.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';
import 'package:gather_point/feature/settings/presentation/views/settings_view.dart';
import 'package:gather_point/feature/home/<USER>/views/place_details_screen.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';

import 'explore_list_view.dart';

class GatherPointHome extends StatefulWidget {
  const GatherPointHome({super.key});

  @override
  State<GatherPointHome> createState() => _GatherPointHomeState();
}

class _GatherPointHomeState extends State<GatherPointHome> {
  City? _currentCity;
  List<City> _cities = [];
  List<ServiceCategory> _categoryItems = [];
  List<Map<String, dynamic>> _reelItems = [];
  List<Map<String, dynamic>> _searchResults = [];
  late final DioConsumer _dioConsumer;

  // Search related variables
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  bool _isLoadingCategories = false;
  bool _isLoadingReels = false;

  @override
  void initState() {
    super.initState();
    _dioConsumer = DioConsumer(
      dio: getIt<Dio>(),
      profileBox: getIt<Box<UserEntity>>(),
    );
    _loadCitiesAndSelectClosest();
    _fetchCategoryContent(1);
    _fetchReelsForNearbyItem(2);
  }

  Future<void> _loadCitiesAndSelectClosest() async {
    final locationService = LocationService();
    try {
      final hasPermission = await locationService.checkLocationPermissions();
      if (!hasPermission) {
        throw Exception('Location permission denied');
      }
      final position = await Geolocator.getCurrentPosition();
      final cities = await locationService.getCitiesAndClosest(position);

      setState(() {
        _currentCity = cities['closestCity'];
        _cities = cities['allCities'];
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              S.of(context).locationPermissionError,
              style: AppTextStyles.font14Regular.copyWith(color: Colors.white),
            ),
          ),
        );
      }
    }
  }

  void _showCityDialog() {
    final s = S.of(context);
    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          backgroundColor: dialogContext.cardColor,
          title: Text(s.selectCity,
              style: AppTextStyles.font18Bold
                  .copyWith(color: dialogContext.primaryTextColor)),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _cities.length,
              itemBuilder: (context, index) {
                final city = _cities[index];
                return ListTile(
                  title: Text(city.name,
                      style: AppTextStyles.font16Regular
                          .copyWith(color: dialogContext.primaryTextColor)),
                  hoverColor: dialogContext.accentColor.withValues(alpha: 0.1),
                  onTap: () {
                    setState(() {
                      _currentCity = city;
                    });
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  Future<void> _fetchCategoryContent(int categoryId) async {
    setState(() {
      _isLoadingCategories = true;
    });

    try {
      final response = await _dioConsumer.get(
        'https://backend.gatherpoint.sa/api/service_categories/list?service_category_id=$categoryId',
      );

      if (response['data'] != null) {
        final List data = response['data'];
        setState(() {
          _categoryItems =
              data.map((json) => ServiceCategory.fromJson(json)).toList();
        });
      }
    } catch (e) {
      print('Failed to load category data: $e');
    } finally {
      setState(() {
        _isLoadingCategories = false;
      });
    }
  }

  Future<void> _fetchReelsForNearbyItem(int itemId) async {
    setState(() {
      _isLoadingReels = true;
    });

    try {
      final response = await _dioConsumer.get(
        'https://backend.gatherpoint.sa/api/items/list?service_category_id=$itemId&reels=1',
      );

      if (response['data'] != null) {
        final List data = response['data'];
        setState(() {
          _reelItems = data.cast<Map<String, dynamic>>();
        });
      }
    } catch (e) {
      print('Failed to load reels data from selected item: $e');
    } finally {
      setState(() {
        _isLoadingReels = false;
      });
    }
  }

  // Search functionality
  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final response = await _dioConsumer.get(
        '/api/items/search',
        queryParameters: {'keyword': query.trim()},
      );

      if (response['data'] != null) {
        final List<Map<String, dynamic>> results =
            List<Map<String, dynamic>>.from(response['data']);
        setState(() {
          _searchResults = results;
        });

        // Show search results in a bottom sheet or navigate to search results page
        if (mounted && results.isNotEmpty) {
          _showSearchResults(results, query.trim());
        }
      } else {
        setState(() {
          _searchResults = [];
        });
        if (mounted) {
          final s = S.of(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                s.noResults,
                style: AppTextStyles.font14Regular.copyWith(color: Colors.white),
              ),
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _searchResults = [];
      });
      if (mounted) {
        final s = S.of(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${s.error}: $e',
              style: AppTextStyles.font14Regular.copyWith(color: Colors.white),
            ),
          ),
        );
      }
    } finally {
      setState(() {
        _isSearching = false;
      });
    }
  }

  // Show search results in a bottom sheet
  void _showSearchResults(List<Map<String, dynamic>> results, String query) {
    final s = S.of(context);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: context.backgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.cardColor,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                boxShadow: [
                  BoxShadow(
                    color: context.accentColor.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.search_rounded,
                    color: context.accentColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '${s.searchResults} "$query"',
                      style: AppTextStyles.font18Bold.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close_rounded,
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            // Results list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: results.length,
                itemBuilder: (context, index) {
                  final item = results[index];
                  return _buildSearchResultCard(item);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build individual search result card
  Widget _buildSearchResultCard(Map<String, dynamic> item) {
    final image = (item['gallery'] as List?)?.isNotEmpty == true
        ? item['gallery'][0]['image']
        : item['image'] ?? 'https://via.placeholder.com/80x80';
    final title = item['title'] ?? '';
    final price = item['price'] ?? 0;
    final city = item['city'] ?? '';
    final rating = item['rating']?.toString() ?? '0';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: context.cardShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            // Don't close bottom sheet, just navigate to place details
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PlaceDetailsScreen(placeData: item),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    image,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: context.secondaryTextColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.image_not_supported_rounded,
                        color: context.secondaryTextColor,
                        size: 32,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.font16SemiBold.copyWith(
                          color: context.primaryTextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      if (city.isNotEmpty)
                        Row(
                          children: [
                            Icon(
                              Icons.location_on_rounded,
                              size: 14,
                              color: context.secondaryTextColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              city,
                              style: AppTextStyles.font12Regular.copyWith(
                                color: context.secondaryTextColor,
                              ),
                            ),
                          ],
                        ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: context.accentColor,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '$price ${S.of(context).perNight}',
                              style: AppTextStyles.font12Bold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const Spacer(),
                          if (rating != '0')
                            Row(
                              children: [
                                const Icon(
                                  Icons.star_rounded,
                                  size: 14,
                                  color: Colors.amber,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  rating,
                                  style: AppTextStyles.font12SemiBold.copyWith(
                                    color: context.primaryTextColor,
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Arrow
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 16,
                  color: context.secondaryTextColor,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Cancel search functionality
  void _cancelSearch() {
    setState(() {
      _isSearching = false;
      _searchResults = [];
    });
    _searchController.clear();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return BlocConsumer<LocaleCubit, LocaleState>(
      listener: (context, state) {
        // Update API headers when language changes
        _dioConsumer.updateLocalization();
        // Refetch data with new language
        _fetchCategoryContent(1);
        _fetchReelsForNearbyItem(2);
      },
      builder: (context, localeState) {
        return Scaffold(
      backgroundColor: context.backgroundColor,
      body: SafeArea(
        child: Padding(
          padding:
              const EdgeInsets.all(5),
          child: ListView(
            children: [
              // Enhanced Header Component
              home_components.EnhancedHomeHeader(
                appName: s.appName,
                currentCity: _currentCity?.name,
                onCityTap: _cities.isNotEmpty ? _showCityDialog : null,
                onSettingsTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const SettingsView(),
                    ),
                  );
                },
                isLoadingLocation: _currentCity == null && _cities.isEmpty,
              ),

              const SizedBox(height: 6),
              // Enhanced Search Bar Component
              home_components.EnhancedSearchBar(
                hintText: s.searchHint,
                showFilter: false,
                isLoading: _isSearching,
                controller: _searchController,
                onChanged: (value) {
                  // Optional: Add debounced search here if needed
                },
                onSearchTap: () {
                  _performSearch(_searchController.text);
                },
                onSubmitted: (value) {
                  _performSearch(value);
                },
                onCancelSearch: _cancelSearch,
              ),

              const SizedBox(height: 10),
              // Enhanced Reels Section Header
              home_components.EnhancedSectionHeader(
                title: s.browseReels,
                subtitle: s.discoverMore,
                icon: Icons.play_circle_outline_rounded,
                onViewAll: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ReelsPage(
                        searchResults: _reelItems,
                        searchQuery: '',
                        serviceCategoryId: 0,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 5),
              SizedBox(
                height: 200,
                child: _isLoadingReels
                    ? ShimmerComponents.reelsList(context)
                    : _reelItems.isEmpty
                        ? _buildEmptyState(
                            context,
                            icon: Icons.play_circle_outline,
                            message: s.noResults,
                          )
                        : ListView.builder(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            itemCount: _reelItems.length,
                            itemBuilder: (context, index) {
                              final item = _reelItems[index];
                              return _buildEnhancedReelCard(item);
                            },
                          ),
              ),

              const SizedBox(height: 10),

              // Enhanced Categories Section Header
              home_components.EnhancedSectionHeader(
                title: s.exploreCategories,
                subtitle: s.exploreAllCategoriesSubtitle, // <-- Localized
                icon: Icons.explore_rounded,
                onViewAll: null,
              ),

              const SizedBox(height: 5),

              SizedBox(
                height: 130,
                child: _isLoadingCategories
                    ? ShimmerComponents.categoriesList(context)
                    : _categoryItems.isEmpty
                        ? _buildEmptyState(
                            context,
                            icon: Icons.explore_outlined,
                            message: s.noResults, // already localized
                          )
                        : ListView.builder(
                            scrollDirection: Axis.horizontal,
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            itemCount: _categoryItems.length,
                            itemBuilder: (context, index) {
                              final item = _categoryItems[index];
                              final title = item.title;
                              return GestureDetector(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => ExploreListScreen(
                                        dioConsumer: _dioConsumer,
                                        categoryId: item.id,
                                        categoryTitle: title,
                                      ),
                                    ),
                                  );
                                },
                                child: _buildEnhancedPlaceCard(
                                    title, item.icon, context),
                              );
                            },
                          ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
      },
    );
  }

  Widget filterButton(String label, String imageUrl, BuildContext context,
      bool selected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
        decoration: BoxDecoration(
          color: selected
              ? context.accentColor
              : (context.isDarkMode ? Colors.grey[800] : Colors.grey[200]),
          borderRadius: BorderRadius.circular(20),
          boxShadow: selected
              ? [
                  BoxShadow(
                    color: context.accentColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: Image.network(
                imageUrl,
                width: 24,
                height: 24,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Icon(
                    Icons.image_not_supported,
                    size: 24,
                    color: context.secondaryTextColor),
              ),
            ),
            const SizedBox(width: 6),
            Text(label,
                style: AppTextStyles.font12Bold.copyWith(
                    color: selected
                        ? (context.isDarkMode
                            ? AppColors.black
                            : AppColors.white)
                        : context.primaryTextColor)),
          ],
        ),
      ),
    );
  }

  Widget reelCard(Map<String, dynamic> item) {
    final image = (item['gallery'] as List).isNotEmpty
        ? item['gallery'][0]['image']
        : 'https://via.placeholder.com/260x180';
    final price = item['price'] ?? 0;

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ReelsPage(
              searchResults: _reelItems,
              searchQuery: '',
              serviceCategoryId: 0,
            ),
          ),
        );
      },
      child: SizedBox(
        width: 260,
        child: Container(
          margin: const EdgeInsets.only(left: 20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            image: DecorationImage(
              image: NetworkImage(image),
              fit: BoxFit.cover,
            ),
          ),
          child: Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Chip(
                label: Text('$price ر.س'),
                backgroundColor: Colors.black87,
                labelStyle:
                    AppTextStyles.font12SemiBold.copyWith(color: Colors.white),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget placeCard(
      String label, String iconUrl, Color color, BuildContext context) {
    return Container(
      width: 120,
      height: 100,
      margin: const EdgeInsets.only(left: 20),
      decoration: BoxDecoration(
        color: context.cardColor,
        border: Border.all(color: color, width: 2),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.network(
            iconUrl,
            width: 60,
            height: 60,
            errorBuilder: (context, error, stackTrace) =>
                Icon(Icons.error, color: context.secondaryTextColor),
          ),
          const SizedBox(height: 10),
          Text(
            label,
            style: AppTextStyles.font14Regular
                .copyWith(color: context.primaryTextColor),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// Enhanced reel card with better UI
  Widget _buildEnhancedReelCard(Map<String, dynamic> item) {
    final image = (item['gallery'] as List).isNotEmpty
        ? item['gallery'][0]['image']
        : 'https://via.placeholder.com/260x180';
    final price = item['price'] ?? 0;
    final title = item['title'] ?? '';

    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ReelsPage(
              searchResults: _reelItems,
              searchQuery: '',
              serviceCategoryId: 0,
            ),
          ),
        );
      },
      child: Container(
        width: 280,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: context.cardShadow,
        ),
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.network(
                image,
                height: 200,
                width: 280,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  height: 200,
                  width: 280,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        context.accentColor.withValues(alpha: 0.3),
                        context.accentColor.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.play_circle_outline,
                    size: 48,
                    color: context.accentColor,
                  ),
                ),
              ),
            ),
            // Gradient overlay
            Container(
              height: 200,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
            ),
            // Content overlay
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title.isNotEmpty)
                    Text(
                      title,
                      style: AppTextStyles.font16Bold.copyWith(
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: context.accentColor,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '$price ${S.of(context).perNight}',
                          style: AppTextStyles.font12Bold.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Empty state widget
  Widget _buildEmptyState(
    BuildContext context, {
    required IconData icon,
    required String message,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              size: 48,
              color: context.accentColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: AppTextStyles.font16Regular.copyWith(
              color: context.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Enhanced place card with better UI
  Widget _buildEnhancedPlaceCard(
      String label, String iconUrl, BuildContext context) {
    return Container(
      width: 90,
      height: 60,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: context.cardShadow,
        border: Border.all(
          color: context.accentColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Image.network(
              iconUrl,
              width: 32,
              height: 32,
              errorBuilder: (context, error, stackTrace) => Icon(
                Icons.category_outlined,
                size: 32,
                color: context.accentColor,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              label,
              style: AppTextStyles.font14Medium.copyWith(
                color: context.primaryTextColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
