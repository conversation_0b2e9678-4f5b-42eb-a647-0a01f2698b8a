import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/services/location_service.dart';
import 'package:gather_point/core/widgets/enhanced_home_components.dart'
    as home_components;
import 'package:gather_point/core/widgets/shimmer_components.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/cubit/home_cubit.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';
import 'package:gather_point/feature/settings/presentation/views/settings_view.dart';
import 'package:gather_point/feature/home/<USER>/views/place_details_screen.dart';
import 'package:hive/hive.dart';

import 'explore_list_view.dart';

class GatherPointHome extends StatelessWidget {
  const GatherPointHome({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HomeCubit(
        dioConsumer: DioConsumer(
          dio: getIt<Dio>(),
          profileBox: getIt<Box<UserEntity>>(),
        ),
        locationService: getIt<LocationService>(),
      )..initializeHome(),
      child: const _GatherPointHomeContent(),
    );
  }
}

class _GatherPointHomeContent extends StatefulWidget {
  const _GatherPointHomeContent();

  @override
  State<_GatherPointHomeContent> createState() => _GatherPointHomeContentState();
}

class _GatherPointHomeContentState extends State<_GatherPointHomeContent> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _showCityDialog(List<City> cities, City? currentCity) {
    final s = S.of(context);
    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          backgroundColor: dialogContext.cardColor,
          title: Text(s.selectCity,
              style: AppTextStyles.font18Bold
                  .copyWith(color: dialogContext.primaryTextColor)),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: cities.length,
              itemBuilder: (context, index) {
                final city = cities[index];
                return ListTile(
                  title: Text(city.name,
                      style: AppTextStyles.font16Regular
                          .copyWith(color: dialogContext.primaryTextColor)),
                  hoverColor: dialogContext.accentColor.withValues(alpha: 0.1),
                  onTap: () {
                    context.read<HomeCubit>().selectCity(city);
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _performSearch(String query) {
    context.read<HomeCubit>().performSearch(query);
  }

  void _clearSearch() {
    _searchController.clear();
    FocusScope.of(context).unfocus();
    context.read<HomeCubit>().clearSearch();
  }

  void _showSearchResults(List<Map<String, dynamic>> results, String query) {
    final s = S.of(context);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: BoxDecoration(
          color: context.backgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.cardColor,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                boxShadow: [
                  BoxShadow(
                    color: context.accentColor.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.search_rounded,
                    color: context.accentColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '${s.searchResults} "$query"',
                      style: AppTextStyles.font18Bold.copyWith(
                        color: context.primaryTextColor,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close_rounded,
                      color: context.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            // Results list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: results.length,
                itemBuilder: (context, index) {
                  final item = results[index];
                  return _buildSearchResultCard(item);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResultCard(Map<String, dynamic> item) {
    final image = (item['gallery'] as List?)?.isNotEmpty == true
        ? item['gallery'][0]['image']
        : item['image'] ?? 'https://via.placeholder.com/80x80';
    final title = item['title'] ?? '';
    final price = item['price'] ?? 0;
    final city = item['city'] ?? '';
    final rating = item['rating']?.toString() ?? '0';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: context.cardShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PlaceDetailsScreen(placeData: item),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.network(
                    image,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: context.secondaryTextColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.image_not_supported_rounded,
                        color: context.secondaryTextColor,
                        size: 32,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.font16SemiBold.copyWith(
                          color: context.primaryTextColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      if (city.isNotEmpty)
                        Row(
                          children: [
                            Icon(
                              Icons.location_on_rounded,
                              size: 14,
                              color: context.secondaryTextColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              city,
                              style: AppTextStyles.font12Regular.copyWith(
                                color: context.secondaryTextColor,
                              ),
                            ),
                          ],
                        ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: context.accentColor,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '$price ${S.of(context).perNight}',
                              style: AppTextStyles.font12Bold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const Spacer(),
                          if (rating != '0')
                            Row(
                              children: [
                                const Icon(
                                  Icons.star_rounded,
                                  size: 14,
                                  color: Colors.amber,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  rating,
                                  style: AppTextStyles.font12SemiBold.copyWith(
                                    color: context.primaryTextColor,
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Arrow
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 16,
                  color: context.secondaryTextColor,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return BlocConsumer<LocaleCubit, LocaleState>(
      listener: (context, localeState) {
        context.read<HomeCubit>().updateLocalization();
      },
      builder: (context, localeState) {
        return BlocConsumer<HomeCubit, HomeState>(
          listener: (context, state) {
            if (state is HomeLoaded && state.searchResults.isNotEmpty) {
              _showSearchResults(state.searchResults, _searchController.text);
            }
          },
          builder: (context, state) {
            if (state is HomeLoading) {
              return Scaffold(
                backgroundColor: context.backgroundColor,
                body: const Center(child: CircularProgressIndicator()),
              );
            }

            if (state is HomeError) {
              return Scaffold(
                backgroundColor: context.backgroundColor,
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: context.secondaryTextColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        state.message,
                        style: AppTextStyles.font16Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          context.read<HomeCubit>().initializeHome();
                        },
                        child: Text(s.retry),
                      ),
                    ],
                  ),
                ),
              );
            }

            final homeState = state as HomeLoaded;
            
            return Scaffold(
              backgroundColor: context.backgroundColor,
              body: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(5),
                  child: ListView(
                    children: [
                      // Enhanced Header Component
                      home_components.EnhancedHomeHeader(
                        appName: s.appName,
                        currentCity: homeState.currentCity?.name,
                        onCityTap: homeState.cities.isNotEmpty 
                            ? () => _showCityDialog(homeState.cities, homeState.currentCity)
                            : null,
                        onSettingsTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SettingsView(),
                            ),
                          );
                        },
                        isLoadingLocation: homeState.currentCity == null && homeState.cities.isEmpty,
                      ),

                      const SizedBox(height: 6),
                      // Enhanced Search Bar Component
                      home_components.EnhancedSearchBar(
                        hintText: s.searchHint,
                        showFilter: false,
                        isLoading: homeState.isSearching,
                        controller: _searchController,
                        onChanged: (value) {
                          setState(() {}); // Trigger rebuild to show/hide clear button
                        },
                        onSearchTap: () {
                          _performSearch(_searchController.text);
                        },
                        onSubmitted: (value) {
                          _performSearch(value);
                        },
                        onClear: _clearSearch,
                      ),

                      const SizedBox(height: 10),

                      // Reels Section
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          children: [
                            Text(
                              s.reels,
                              style: AppTextStyles.font20Bold.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const Spacer(),
                            TextButton(
                              onPressed: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const ReelsPage(
                                      serviceCategoryId: 2,
                                      searchResults: [],
                                      searchQuery: '',
                                    ),
                                  ),
                                );
                              },
                              child: Text(
                                s.seeAll,
                                style: AppTextStyles.font14SemiBold.copyWith(
                                  color: context.accentColor,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 5),
                      SizedBox(
                        height: 200,
                        child: homeState.isLoadingReels
                            ? ShimmerComponents.reelsList(context)
                            : homeState.reels.isEmpty
                                ? _buildEmptyState(
                                    context,
                                    icon: Icons.play_circle_outline,
                                    message: s.noResults,
                                  )
                                : ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.symmetric(horizontal: 4),
                                    itemCount: homeState.reels.length,
                                    itemBuilder: (context, index) {
                                      final item = homeState.reels[index];
                                      return _buildEnhancedReelCard(item);
                                    },
                                  ),
                      ),

                      const SizedBox(height: 20),

                      // Categories Section
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          children: [
                            Text(
                              s.categories,
                              style: AppTextStyles.font20Bold.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 5),
                      SizedBox(
                        height: 130,
                        child: homeState.isLoadingCategories
                            ? ShimmerComponents.categoriesList(context)
                            : homeState.categories.isEmpty
                                ? _buildEmptyState(
                                    context,
                                    icon: Icons.explore_outlined,
                                    message: s.noResults,
                                  )
                                : ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    padding: const EdgeInsets.symmetric(horizontal: 4),
                                    itemCount: homeState.categories.length,
                                    itemBuilder: (context, index) {
                                      final item = homeState.categories[index];
                                      final title = item.title;
                                      return GestureDetector(
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => ExploreListScreen(
                                                dioConsumer: DioConsumer(
                                                  dio: getIt<Dio>(),
                                                  profileBox: getIt<Box<UserEntity>>(),
                                                ),
                                                categoryId: item.id,
                                                categoryTitle: title,
                                              ),
                                            ),
                                          );
                                        },
                                        child: _buildEnhancedPlaceCard(
                                            title, item.icon, context),
                                      );
                                    },
                                  ),
                      ),

                      const SizedBox(height: 100), // Bottom padding for navigation
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context, {required IconData icon, required String message}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 48,
            color: context.secondaryTextColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 12),
          Text(
            message,
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedReelCard(Map<String, dynamic> item) {
    final image = (item['gallery'] as List?)?.isNotEmpty == true
        ? item['gallery'][0]['image']
        : item['image'] ?? 'https://via.placeholder.com/280x200';
    final title = item['title'] ?? '';
    final city = item['city'] ?? '';

    return Container(
      width: 280,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: context.cardShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PlaceDetailsScreen(placeData: item),
              ),
            );
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // Background Image
                Image.network(
                  image,
                  width: 280,
                  height: 200,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 280,
                    height: 200,
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.image_not_supported_rounded,
                      color: context.secondaryTextColor,
                      size: 48,
                    ),
                  ),
                ),
                // Gradient Overlay
                Container(
                  width: 280,
                  height: 200,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                ),
                // Content
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.font16Bold.copyWith(
                          color: Colors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (city.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(
                              Icons.location_on_rounded,
                              size: 14,
                              color: Colors.white70,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              city,
                              style: AppTextStyles.font12Regular.copyWith(
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                // Play Button
                const Positioned(
                  top: 16,
                  right: 16,
                  child: Icon(
                    Icons.play_circle_filled,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedPlaceCard(String title, String? iconUrl, BuildContext context) {
    return Container(
      width: 90,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: context.cardShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: context.accentColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: iconUrl != null && iconUrl.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.network(
                            iconUrl,
                            width: 40,
                            height: 40,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              Icons.category_rounded,
                              color: context.accentColor,
                              size: 24,
                            ),
                          ),
                        )
                      : Icon(
                          Icons.category_rounded,
                          color: context.accentColor,
                          size: 24,
                        ),
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: AppTextStyles.font12SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
