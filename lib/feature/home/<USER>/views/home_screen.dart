import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/managers/locale_cubit/locale_cubit.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/services/location_service.dart';
import 'package:gather_point/core/widgets/enhanced_home_components.dart' as home_components;
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/cubit/home_cubit.dart';
import 'package:gather_point/feature/home/<USER>/widgets/home_reels_section.dart';
import 'package:gather_point/feature/home/<USER>/widgets/home_categories_section.dart';
import 'package:gather_point/feature/home/<USER>/widgets/home_search_results.dart';
import 'package:gather_point/feature/settings/presentation/views/settings_view.dart';
import 'package:hive/hive.dart';

class GatherPointHome extends StatelessWidget {
  const GatherPointHome({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HomeCubit(
        dioConsumer: DioConsumer(
          dio: getIt<Dio>(),
          profileBox: getIt<Box<UserEntity>>(),
        ),
        locationService: getIt<LocationService>(),
      )..initializeHome(),
      child: const _GatherPointHomeContent(),
    );
  }
}

class _GatherPointHomeContent extends StatefulWidget {
  const _GatherPointHomeContent();

  @override
  State<_GatherPointHomeContent> createState() => _GatherPointHomeContentState();
}

class _GatherPointHomeContentState extends State<_GatherPointHomeContent> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _showCityDialog(List<City> cities, City? currentCity) {
    final s = S.of(context);
    // Capture the HomeCubit reference before showing dialog
    final homeCubit = context.read<HomeCubit>();

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          backgroundColor: dialogContext.cardColor,
          title: Text(s.selectCity,
              style: AppTextStyles.font18Bold
                  .copyWith(color: dialogContext.primaryTextColor)),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: cities.length,
              itemBuilder: (context, index) {
                final city = cities[index];
                return ListTile(
                  title: Text(city.name,
                      style: AppTextStyles.font16Regular
                          .copyWith(color: dialogContext.primaryTextColor)),
                  hoverColor: dialogContext.accentColor.withValues(alpha: 0.1),
                  onTap: () {
                    debugPrint('User selected city: ${city.name} (ID: ${city.id})');
                    // Use the captured cubit reference instead of context.read
                    homeCubit.selectCity(city);
                    Navigator.pop(dialogContext);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _performSearch(String query) {
    try {
      context.read<HomeCubit>().performSearch(query);
    } catch (e) {
      debugPrint('HomeCubit not available for search: $e');
    }
  }

  void _clearSearch() {
    _searchController.clear();
    FocusScope.of(context).unfocus();
    try {
      context.read<HomeCubit>().clearSearch();
    } catch (e) {
      debugPrint('HomeCubit not available for clear search: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return BlocConsumer<HomeCubit, HomeState>(
      listener: (context, state) {
        if (state is HomeLoaded && state.searchResults.isNotEmpty) {
          HomeSearchResults.show(context, state.searchResults, _searchController.text);
        }
      },
      builder: (context, state) {
        return BlocListener<LocaleCubit, LocaleState>(
          listener: (context, localeState) {
            // Only call updateLocalization if HomeCubit is available
            try {
              context.read<HomeCubit>().updateLocalization();
            } catch (e) {
              // HomeCubit not available yet, ignore
              debugPrint('HomeCubit not available for locale update: $e');
            }
          },
          child: Builder(
            builder: (context) {
            if (state is HomeLoading) {
              return _buildLoadingScreen(context);
            }

            if (state is HomeError) {
              return _buildErrorScreen(context, state.message, s);
            }

            final homeState = state as HomeLoaded;

            // Debug: Print current state
            debugPrint('Home State - City: ${homeState.currentCity?.name}, Categories: ${homeState.categories.length}, Reels: ${homeState.reels.length}');

            return Scaffold(
              backgroundColor: context.backgroundColor,
              body: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(5),
                  child: ListView(
                    children: [
                      // Enhanced Header Component
                      home_components.EnhancedHomeHeader(
                        appName: s.appName,
                        currentCity: homeState.currentCity?.name,
                        onCityTap: homeState.cities.isNotEmpty 
                            ? () => _showCityDialog(homeState.cities, homeState.currentCity)
                            : null,
                        onSettingsTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SettingsView(),
                            ),
                          );
                        },
                        isLoadingLocation: homeState.currentCity == null && homeState.cities.isEmpty,
                      ),

                      const SizedBox(height: 6),
                      
                      // Enhanced Search Bar Component
                      home_components.EnhancedSearchBar(
                        hintText: s.searchHint,
                        showFilter: false,
                        isLoading: homeState.isSearching,
                        controller: _searchController,
                        onChanged: (value) {
                          setState(() {}); // Trigger rebuild to show/hide clear button
                        },
                        onSearchTap: () {
                          _performSearch(_searchController.text);
                        },
                        onSubmitted: (value) {
                          _performSearch(value);
                        },
                        onClear: _clearSearch,
                      ),

                      const SizedBox(height: 10),
                      
                      // Reels Section
                      HomeReelsSection(
                        reels: homeState.reels,
                        isLoading: homeState.isLoadingReels,
                      ),

                      const SizedBox(height: 20),
                      
                      // Categories Section
                      HomeCategoriesSection(
                        categories: homeState.categories,
                        isLoading: homeState.isLoadingCategories,
                      ),

                      const SizedBox(height: 100), // Bottom padding for navigation
                    ],
                  ),
                ),
              ),
            );
            },
          ),
        );
      },
    );
  }

  Widget _buildLoadingScreen(BuildContext context) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildErrorScreen(BuildContext context, String message, S s) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: context.secondaryTextColor,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.font16Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                try {
                  context.read<HomeCubit>().initializeHome();
                } catch (e) {
                  debugPrint('HomeCubit not available for retry: $e');
                  // Optionally navigate back or show a different error
                }
              },
              child: Text(s.retry),
            ),
          ],
        ),
      ),
    );
  }
}
