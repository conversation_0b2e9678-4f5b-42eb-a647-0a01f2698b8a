import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/location_service.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'dart:developer' as developer;

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  final DioConsumer _dioConsumer;
  final LocationService _locationService;

  HomeCubit({
    required DioConsumer dioConsumer,
    required LocationService locationService,
  })  : _dioConsumer = dioConsumer,
        _locationService = locationService,
        super(HomeInitial());

  /// Safe emit that checks if cubit is not closed
  void _safeEmit(HomeState state) {
    if (!isClosed) {
      emit(state);
    }
  }

  /// Initialize home data
  Future<void> initializeHome() async {
    _safeEmit(HomeLoading());

    try {
      // First, load cities (this must happen first)
      await loadCitiesAndSelectClosest();

      // Wait a bit to ensure cities are loaded
      await Future.delayed(const Duration(milliseconds: 100));

      // Get current state to access selected city
      final currentState = state;
      if (currentState is HomeLoaded) {
        // Load data with selected city (or null if no city)
        await Future.wait([
          fetchCategories(currentState.currentCity?.id),
          fetchReels(currentState.currentCity?.id),
        ]);
      }
    } catch (e) {
      _safeEmit(HomeError('Failed to initialize home: ${e.toString()}'));
    }
  }

  /// Load cities and select closest based on location
  Future<void> loadCitiesAndSelectClosest() async {
    try {
      final hasPermission = await _locationService.checkLocationPermissions();

      if (hasPermission) {
        // User granted location permission - get current location
        try {
          final position = await Geolocator.getCurrentPosition();
          final cities = await _locationService.getCitiesAndClosest(position);

          final currentState = state;
          if (currentState is HomeLoaded) {
            _safeEmit(currentState.copyWith(
              currentCity: cities['closestCity'],
              cities: cities['allCities'],
            ));
          } else {
            _safeEmit(HomeLoaded(
              currentCity: cities['closestCity'],
              cities: cities['allCities'],
            ));
          }
          return;
        } catch (e) {
          developer.log('Failed to get current location: $e', name: 'HomeCubit');
          // Fall through to load cities without location
        }
      }

      // No permission or location failed - load cities with default location (Riyadh)
      await loadCitiesWithDefaultLocation();

    } catch (e) {
      developer.log('Failed to load cities: $e', name: 'HomeCubit');
      // Continue with empty cities list if everything fails
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(cities: []));
      } else {
        _safeEmit(const HomeLoaded(cities: []));
      }
    }
  }

  /// Load cities using default location (Riyadh coordinates)
  Future<void> loadCitiesWithDefaultLocation() async {
    try {
      // Use Riyadh coordinates as default
      const defaultLat = 24.7136;
      const defaultLng = 46.6753;

      final response = await _dioConsumer.post(
        '/api/general/cities',
        data: {'lat': defaultLat, 'lng': defaultLng},
      );

      if (response['data'] != null) {
        final data = response['data'];
        final citiesList = (data['cities'] as List)
            .map((json) => City.fromJson(json))
            .toList();

        // Select first city as default (usually closest to Riyadh)
        final defaultCity = citiesList.isNotEmpty ? citiesList.first : null;

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            currentCity: defaultCity,
            cities: citiesList,
          ));
        } else {
          _safeEmit(HomeLoaded(
            currentCity: defaultCity,
            cities: citiesList,
          ));
        }
      }
    } catch (e) {
      developer.log('Failed to load cities with default location: $e', name: 'HomeCubit');
      rethrow;
    }
  }

  /// Select a specific city and refetch data
  Future<void> selectCity(City city) async {
    final currentState = state;
    if (currentState is HomeLoaded) {
      // Update current city immediately
      _safeEmit(currentState.copyWith(currentCity: city));

      // Refresh data with new city
      await Future.wait([
        fetchCategories(city.id),
        fetchReels(city.id),
      ]);
    }
  }

  /// Fetch categories with optional city filter
  Future<void> fetchCategories(int? cityId) async {
    final currentState = state;
    if (currentState is HomeLoaded) {
      _safeEmit(currentState.copyWith(isLoadingCategories: true));
    }

    try {
      final queryParams = <String, dynamic>{
        'service_category_id': 1,
      };

      if (cityId != null) {
        queryParams['city_id'] = cityId;
      }

      final response = await _dioConsumer.get(
        '/api/service_categories/list',
        queryParameters: queryParams,
      );

      if (response['data'] != null) {
        final List data = response['data'];
        final categories = data.map((json) => ServiceCategory.fromJson(json)).toList();

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            categories: categories,
            isLoadingCategories: false,
          ));
        }
      }
    } catch (e) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(isLoadingCategories: false));
      }
      // Don't emit error for individual operations, just log
      developer.log('Failed to load categories: $e', name: 'HomeCubit');
    }
  }

  /// Fetch reels with optional city filter
  Future<void> fetchReels(int? cityId) async {
    final currentState = state;
    if (currentState is HomeLoaded) {
      _safeEmit(currentState.copyWith(isLoadingReels: true));
    }

    try {
      final queryParams = <String, dynamic>{
        'service_category_id': 2,
        'reels': 1,
      };

      if (cityId != null) {
        queryParams['city_id'] = cityId;
      }

      final response = await _dioConsumer.get(
        '/api/items/list',
        queryParameters: queryParams,
      );

      if (response['data'] != null) {
        final List data = response['data'];
        final reels = data.cast<Map<String, dynamic>>();

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            reels: reels,
            isLoadingReels: false,
          ));
        }
      }
    } catch (e) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(isLoadingReels: false));
      }
      // Don't emit error for individual operations, just log
      developer.log('Failed to load reels: $e', name: 'HomeCubit');
    }
  }

  /// Perform search
  Future<void> performSearch(String query) async {
    if (query.trim().isEmpty) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(searchResults: []));
      }
      return;
    }

    final currentState = state;
    if (currentState is HomeLoaded) {
      _safeEmit(currentState.copyWith(isSearching: true));
    }

    try {
      final response = await _dioConsumer.get(
        '/api/items/search',
        queryParameters: {'keyword': query.trim()},
      );

      if (response['data'] != null) {
        final List<Map<String, dynamic>> results =
            List<Map<String, dynamic>>.from(response['data']);

        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            searchResults: results,
            isSearching: false,
          ));
        }
      } else {
        final currentState = state;
        if (currentState is HomeLoaded) {
          _safeEmit(currentState.copyWith(
            searchResults: [],
            isSearching: false,
          ));
        }
      }
    } catch (e) {
      final currentState = state;
      if (currentState is HomeLoaded) {
        _safeEmit(currentState.copyWith(
          searchResults: [],
          isSearching: false,
        ));
      }
    }
  }

  /// Clear search results
  void clearSearch() {
    final currentState = state;
    if (currentState is HomeLoaded) {
      _safeEmit(currentState.copyWith(searchResults: []));
    }
  }

  /// Update localization and refresh data
  void updateLocalization() {
    _dioConsumer.updateLocalization();
    
    final currentState = state;
    if (currentState is HomeLoaded) {
      // Refresh data with current city
      Future.wait([
        fetchCategories(currentState.currentCity?.id),
        fetchReels(currentState.currentCity?.id),
      ]);
    }
  }
}
