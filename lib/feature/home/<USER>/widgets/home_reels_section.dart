import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/shimmer_components.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/home/<USER>/widgets/reel_card_widget.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';

class HomeReelsSection extends StatelessWidget {
  final List<Map<String, dynamic>> reels;
  final bool isLoading;

  const HomeReelsSection({
    super.key,
    required this.reels,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Column(
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                s.reels,
                style: AppTextStyles.font20Bold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ReelsPage(
                        serviceCategoryId: 2,
                        searchResults: [],
                        searchQuery: '',
                      ),
                    ),
                  );
                },
                child: Text(
                  s.seeAll,
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.accentColor,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 5),

        // Reels List
        SizedBox(
          height: 200,
          child: isLoading
              ? ShimmerComponents.reelsList(context)
              : reels.isEmpty
                  ? _buildEmptyState(context)
                  : ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      itemCount: reels.length,
                      itemBuilder: (context, index) {
                        final item = reels[index];
                        return ReelCardWidget(item: item);
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_circle_outline,
            size: 48,
            color: context.secondaryTextColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 12),
          Text(
            S.of(context).noResults,
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }
}
