import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/widgets/shimmer_components.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/home/<USER>/widgets/category_card_widget.dart';
import 'package:gather_point/feature/home/<USER>/views/explore_list_view.dart';
import 'package:hive/hive.dart';

class HomeCategoriesSection extends StatelessWidget {
  final List<ServiceCategory> categories;
  final bool isLoading;

  const HomeCategoriesSection({
    super.key,
    required this.categories,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Column(
      children: [
        // Section Header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                s.categories,
                style: AppTextStyles.font20Bold.copyWith(
                  color: context.primaryTextColor,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 5),

        // Categories Grid
        SizedBox(
          height: 250, // Increased height for the new card design
          child: isLoading
              ? ShimmerComponents.categoriesList(context)
              : categories.isEmpty
                  ? _buildEmptyState(context)
                  : ListView.builder(
                      scrollDirection: Axis.horizontal,
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      itemCount: categories.length,
                      itemBuilder: (context, index) {
                        final item = categories[index];
                        return GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ExploreListScreen(
                                  dioConsumer: DioConsumer(
                                    dio: getIt<Dio>(),
                                    profileBox: getIt<Box<UserEntity>>(),
                                  ),
                                  categoryId: item.id,
                                  categoryTitle: item.title,
                                ),
                              ),
                            );
                          },
                          child: CategoryCardWidget(
                            title: item.title,
                            imageUrl: item.image,
                          ),
                        );
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.explore_outlined,
            size: 48,
            color: context.secondaryTextColor.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 12),
          Text(
            S.of(context).noResults,
            style: AppTextStyles.font14Regular.copyWith(
              color: context.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }
}
