import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/feature/home/<USER>/views/place_details_screen.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/utils/sound_manager.dart';

class ReelCardWidget extends StatelessWidget {
  final Map<String, dynamic> item;

  const ReelCardWidget({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    final image = (item['gallery'] as List?)?.isNotEmpty == true
        ? item['gallery'][0]['image']
        : item['image'] ?? 'https://via.placeholder.com/280x200';
    final title = item['title'] ?? '';

    return Container(
      width: 280,
      margin: const EdgeInsets.only(right: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: context.cardShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () async {
            // Play click sound
            try {
              await SoundManager.playClickSound();
            } catch (e) {
              debugPrint('Click sound error: $e');
            }

            if (context.mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PlaceDetailsScreen(placeData: item),
                ),
              );
            }
          },
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // Background Image
                Image.network(
                  image,
                  width: 280,
                  height: 200,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 280,
                    height: 200,
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    child: Icon(
                      Icons.image_not_supported_rounded,
                      color: context.secondaryTextColor,
                      size: 48,
                    ),
                  ),
                ),
                // Gradient Overlay
                Container(
                  width: 280,
                  height: 200,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.7),
                      ],
                    ),
                  ),
                ),
                // Content overlay - EXACT MATCH TO ORIGINAL
                Positioned(
                  bottom: 16,
                  left: 16,
                  right: 16,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (title.isNotEmpty)
                        Text(
                          title,
                          style: AppTextStyles.font16Bold.copyWith(
                            color: Colors.white,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: context.accentColor,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              '${item['price'] ?? 0} ${S.of(context).perNight}',
                              style: AppTextStyles.font12Bold.copyWith(
                                color: Colors.white,
                              ),
                            ),
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Icons.play_arrow,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
