import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/feature/reviews/presentation/cubit/review_cubit.dart';
import 'package:gather_point/feature/reviews/data/services/reviews_api_service.dart';
import 'package:gather_point/core/services/service_locator.dart';

class CreateReviewPage extends StatelessWidget {
  final int propertyId;
  final int reservationId;
  final String propertyName;
  final String? propertyImage;

  const CreateReviewPage({
    super.key,
    required this.propertyId,
    required this.reservationId,
    required this.propertyName,
    this.propertyImage,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ReviewCubit(getIt<ReviewsApiService>()),
      child: _CreateReviewPageContent(
        propertyId: propertyId,
        reservationId: reservationId,
        propertyName: propertyName,
        propertyImage: propertyImage,
      ),
    );
  }
}

class _CreateReviewPageContent extends StatefulWidget {
  final int propertyId;
  final int reservationId;
  final String propertyName;
  final String? propertyImage;

  const _CreateReviewPageContent({
    required this.propertyId,
    required this.reservationId,
    required this.propertyName,
    this.propertyImage,
  });

  @override
  State<_CreateReviewPageContent> createState() => _CreateReviewPageContentState();
}

class _CreateReviewPageContentState extends State<_CreateReviewPageContent> {
  final _commentController = TextEditingController();
  double _rating = 5.0;
  final List<File> _images = [];
  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return EnhancedPageLayout(
      title: s.leaveReview,
      body: BlocConsumer<ReviewCubit, ReviewState>(
        listener: (context, state) {
          if (state is ReviewSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إرسال التقييم بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(context).pop(true);
          } else if (state is ReviewError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Property Info
                _buildPropertyInfo(),
                
                const SizedBox(height: 24),
                
                // Rating Section
                _buildRatingSection(),
                
                const SizedBox(height: 24),
                
                // Comment Section
                _buildCommentSection(),
                
                const SizedBox(height: 24),
                
                // Images Section
                _buildImagesSection(),
                
                const SizedBox(height: 32),
                
                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: EnhancedButton(
                    text: 'إرسال التقييم',
                    onPressed: state is ReviewLoading ? null : _submitReview,
                    icon: Icons.send_rounded,
                  ),
                ),
                
                const SizedBox(height: 100), // Bottom padding
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPropertyInfo() {
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Property Image
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: widget.propertyImage != null
                  ? Image.network(
                      widget.propertyImage!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          width: 60,
                          height: 60,
                          color: Colors.grey[300],
                          child: const Icon(Icons.home, color: Colors.grey),
                        );
                      },
                    )
                  : Container(
                      width: 60,
                      height: 60,
                      color: Colors.grey[300],
                      child: const Icon(Icons.home, color: Colors.grey),
                    ),
            ),
            
            const SizedBox(width: 16),
            
            // Property Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.propertyName,
                    style: AppTextStyles.font16Bold.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark 
                          ? Colors.white 
                          : Colors.black,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'كيف كانت تجربتك؟',
                    style: AppTextStyles.font12Regular.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقييم',
          style: AppTextStyles.font18Bold.copyWith(
            color: Theme.of(context).brightness == Brightness.dark 
                ? Colors.white 
                : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        
        EnhancedCard(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Star Rating
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _rating = (index + 1).toDouble();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        child: Icon(
                          index < _rating ? Icons.star : Icons.star_border,
                          color: Colors.amber,
                          size: 40,
                        ),
                      ),
                    );
                  }),
                ),
                
                const SizedBox(height: 16),
                
                // Rating Text
                Text(
                  _getRatingText(_rating),
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color: _getRatingColor(_rating),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCommentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التعليق',
          style: AppTextStyles.font18Bold.copyWith(
            color: Theme.of(context).brightness == Brightness.dark 
                ? Colors.white 
                : Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        
        TextFormField(
          controller: _commentController,
          maxLines: 5,
          decoration: InputDecoration(
            hintText: 'شاركنا تجربتك مع هذا العقار...',
            border: const OutlineInputBorder(),
            filled: true,
            fillColor: Colors.grey.withValues(alpha: 0.05),
          ),
        ),
      ],
    );
  }

  Widget _buildImagesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الصور (اختياري)',
              style: AppTextStyles.font18Bold.copyWith(
                color: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.white 
                    : Colors.black,
              ),
            ),
            TextButton.icon(
              onPressed: _pickImages,
              icon: const Icon(Icons.add_photo_alternate),
              label: const Text('إضافة صور'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        if (_images.isNotEmpty)
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _images.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _images[index],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _images.removeAt(index);
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          )
        else
          Container(
            height: 100,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.withValues(alpha: 0.1),
            ),
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.photo_library, size: 32, color: Colors.grey),
                SizedBox(height: 4),
                Text('لا توجد صور'),
              ],
            ),
          ),
      ],
    );
  }

  Future<void> _pickImages() async {
    final List<XFile> images = await _picker.pickMultiImage();
    if (images.isNotEmpty) {
      setState(() {
        _images.addAll(images.map((image) => File(image.path)));
      });
    }
  }

  void _submitReview() {
    if (_commentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى كتابة تعليق'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    context.read<ReviewCubit>().createReview(
      propertyId: widget.propertyId,
      reservationId: widget.reservationId,
      rating: _rating,
      comment: _commentController.text.trim(),
      images: _images.isNotEmpty ? _images : null,
    );
  }

  String _getRatingText(double rating) {
    switch (rating.toInt()) {
      case 1:
        return 'ضعيف';
      case 2:
        return 'مقبول';
      case 3:
        return 'جيد';
      case 4:
        return 'جيد جداً';
      case 5:
        return 'ممتاز';
      default:
        return 'ممتاز';
    }
  }

  Color _getRatingColor(double rating) {
    switch (rating.toInt()) {
      case 1:
      case 2:
        return Colors.red;
      case 3:
        return Colors.orange;
      case 4:
      case 5:
        return Colors.green;
      default:
        return Colors.green;
    }
  }
}
